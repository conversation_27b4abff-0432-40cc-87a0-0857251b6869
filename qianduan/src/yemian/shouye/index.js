import React, { useEffect } from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../zujian/minganbuju/yangshihuazujian.js';
import {
  meiti_chaxun,
  useShebeileixingJiance,
  useYidongduanJiance,
  usePingbanJiance,
  useZhuomianduanJiance
} from '../../gongju/shebeishiPei_gongju.js';
import Lunboguanggao from './lunboguanggao.js';
import Gengxinlan from './gengxinlan.js';
import Guaiwuliebiao from './guaiwuliebiao.js';

// 首页主容器
const S<PERSON><PERSON><PERSON> = styled(<PERSON><PERSON>nqi)`
  min-height: 100vh;
  padding: 80px 0 40px 0;
  display: flex;
  position: relative;

  ${meiti_chaxun.shouji} {
    flex-direction: column;
    padding: 0;
    align-items: center;
    justify-content: flex-start;
  }

  ${meiti_chaxun.pingban} {
    flex-direction: column;
    padding: 0;
    align-items: center;
    justify-content: flex-start;
  }
`;

// 左侧广告区域
const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>quyu = styled.div`
  width: 480px;
  position: fixed;
  top: 80px;
  left: 10px;
  bottom: 10px;
  height: auto;
  max-height: calc(100vh - 90px);
  overflow: visible;
  margin: 0;
  padding: 0;
  z-index: 50;

  ${meiti_chaxun.shouji} {
    width: 100%;
    position: static;
    height: auto;
    max-height: none;
    overflow: visible;
    margin: 0 auto;
    padding: 65px 5px 10px 5px;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 6px;

    /* 手机端完全不显示滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none;
  }

  ${meiti_chaxun.pingban} {
    width: 100%;
    max-width: 800px;
    position: static;
    height: auto;
    max-height: none;
    overflow: visible;
    margin: 0 auto;
    padding: 70px 15px 10px 15px;
    left: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    gap: 8px;

    /* 平板端也不显示滚动条 */
    &::-webkit-scrollbar {
      display: none;
    }
    scrollbar-width: none;
  }
`;

// 主内容区域
const Zhuneirongquyu = styled.div`
  flex: 1;
  margin-left: 500px;
  padding: 0; /* 完全去掉padding，给内容更多空间 */
  width: calc(100vw - 500px); /* 恢复原始宽度计算 */
  overflow: visible; /* 改为visible，避免内容被裁剪 */
  box-sizing: border-box; /* 确保padding计算在宽度内 */

  ${meiti_chaxun.shouji} {
    margin-left: 0;
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch; /* 改为stretch，让子元素占满宽度 */
    overflow: visible;
    box-sizing: border-box;
  }

  ${meiti_chaxun.pingban} {
    margin-left: 0;
    margin: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: stretch; /* 改为stretch，让子元素占满宽度 */
    overflow: visible;
    box-sizing: border-box;
  }

  /* 桌面端使用全部剩余宽度 */
  ${meiti_chaxun.zhuomian} {
    width: calc(100vw - 500px);
    max-width: none;
    overflow: visible;
  }
`;

// 首页组件
function Shouye() {
  // 使用设备适配功能
  const { huoqu_shebeileixing, shuchu_tiaoshi_xinxi } = useShebeileixingJiance();
  const { jiance_yidongduan } = useYidongduanJiance();
  const { jiance_pingban } = usePingbanJiance();
  const { jiance_zhuomianduan } = useZhuomianduanJiance();

  // 在开发环境下输出设备检测调试信息
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🏠 [首页] 设备适配信息:');
      console.log('📱 设备类型:', huoqu_shebeileixing());
      console.log('📱 是否移动端:', jiance_yidongduan());
      console.log('📱 是否平板:', jiance_pingban());
      console.log('🖥️ 是否桌面端:', jiance_zhuomianduan());

      // 输出详细调试信息
      shuchu_tiaoshi_xinxi();
    }
  }, [huoqu_shebeileixing, jiance_yidongduan, jiance_pingban, jiance_zhuomianduan, shuchu_tiaoshi_xinxi]);

  return (
    <Shouyerongqi>
      {/* 左侧广告区域 */}
      <Zuoceguanggaoquyu>
        <Lunboguanggao />
        <Gengxinlan />
      </Zuoceguanggaoquyu>

      {/* 主内容区域 */}
      <Zhuneirongquyu>
        {/* 怪物数据列表 */}
        <Guaiwuliebiao />
      </Zhuneirongquyu>
    </Shouyerongqi>
  );
}

export default Shouye;
